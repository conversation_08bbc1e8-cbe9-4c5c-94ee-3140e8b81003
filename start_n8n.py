import subprocess
import webbrowser
import time
import os
import socket
import tempfile

# 可选的端口列表
possible_ports = [5679, 5680, 5681, 5682, 8080, 8081, 3000, 3001, 3002]
n8n_url = None

def check_port_available(port):
    """检查端口是否可用"""
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.settimeout(1)
            result = s.connect_ex(('localhost', port))
            return result != 0  # 如果连接失败，端口可用
    except:
        return False

def find_available_port():
    """找到一个可用的端口"""
    for port in possible_ports:
        if check_port_available(port):
            return port
    return None

def wait_for_service(port, max_wait=60):
    """等待服务在指定端口启动"""
    print(f"等待n8n服务在端口{port}启动...")
    print("这可能需要一些时间，请耐心等待...")
    
    for i in range(max_wait):
        if not check_port_available(port):  # 如果端口被占用，说明服务启动了
            print(f"n8n服务已在端口{port}启动!")
            return True
        time.sleep(1)
        if (i + 1) % 10 == 0:
            print(f"已等待{i + 1}秒...")
    return False

def create_batch_file(port):
    """创建批处理文件来启动n8n"""
    batch_content = f"""@echo off
set N8N_PORT={port}
echo Starting n8n on port {port}...
npx n8n start
pause
"""
    
    # 创建临时批处理文件
    with tempfile.NamedTemporaryFile(mode='w', suffix='.bat', delete=False) as f:
        f.write(batch_content)
        return f.name

def start_n8n_and_open_browser():
    global n8n_url
    
    print("启动n8n服务...")
    
    # 检查5678端口状态
    if not check_port_available(5678):
        print("警告: 默认端口5678已被占用!")
        print("正在查找其他可用端口...")
    
    # 查找可用端口
    available_port = find_available_port()
    if not available_port:
        print("错误: 找不到可用端口!")
        print(f"尝试的端口: {possible_ports}")
        print("请手动停止占用这些端口的进程，或者使用其他端口。")
        return
    
    n8n_url = f"http://localhost:{available_port}"
    
    print(f"使用端口: {available_port}")
    
    try:
        if os.name == 'nt':
            # Windows - 创建批处理文件启动
            batch_file = create_batch_file(available_port)
            print(f"创建启动脚本: {batch_file}")
            print("正在启动n8n...")
            
            # 在新窗口中运行批处理文件
            subprocess.Popen(['cmd', '/c', 'start', 'cmd', '/k', batch_file], shell=True)
            
            # 等待一下再删除批处理文件
            time.sleep(2)
            
        else:
            # Linux/macOS 语法
            n8n_command = f'N8N_PORT={available_port} npx n8n start'
            print(f"尝试启动 n8n 服务...")
            print(f"命令: {n8n_command}")
            subprocess.Popen(n8n_command, shell=True, preexec_fn=os.setpgrp)
        
        # 等待服务启动
        if wait_for_service(available_port):
            print(f"n8n启动成功!")
            print(f"正在打开浏览器并访问: {n8n_url}")
            webbrowser.open(n8n_url)
            print("脚本执行完毕。")
            print(f"如果浏览器没有自动打开，请手动访问: {n8n_url}")
            
            # 清理临时文件
            if os.name == 'nt':
                try:
                    os.unlink(batch_file)
                except:
                    pass
        else:
            print(f"错误: n8n服务未能在60秒内启动")
            print("请检查以下几点:")
            print("1. 确保Node.js已正确安装")
            print("2. 检查新打开的控制台窗口中的错误信息")
            print("3. 手动运行以下命令进行测试:")
            if os.name == 'nt':
                print(f"   set N8N_PORT={available_port} && npx n8n start")
            else:
                print(f"   N8N_PORT={available_port} npx n8n start")
            print(f"4. 如果成功启动，请手动访问: {n8n_url}")
            
    except FileNotFoundError:
        print(f"错误: 命令 'npx' 未找到。请确保 Node.js 和 npm 已安装并在您的 PATH 中。")
        return
    except Exception as e:
        print(f"启动 n8n 时发生错误: {e}")
        return

if __name__ == "__main__":
    start_n8n_and_open_browser() 