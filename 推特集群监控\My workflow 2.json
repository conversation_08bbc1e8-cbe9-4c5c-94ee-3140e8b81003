{"name": "My workflow 2", "nodes": [{"parameters": {"rule": {"interval": [{"field": "minutes", "minutesInterval": 15}]}}, "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1.2, "position": [224, -1392], "id": "aa56b13c-e60b-4072-b0ae-dcaee6ca3ab1", "name": "Schedule Trigger4"}, {"parameters": {"chatId": "-*************", "text": "={{ $json.output }}", "additionalFields": {}}, "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [736, -1392], "id": "0a30b516-b704-4fb2-8b4a-779543284a4a", "name": "Telegram", "webhookId": "1d093cff-75e4-441a-ab2e-705b28b2cadb", "retryOnFail": false, "credentials": {"telegramApi": {"id": "oB5w3cMt9eiorzrj", "name": "Telegram account 2"}}, "onError": "continueRegularOutput"}, {"parameters": {"resource": "<PERSON><PERSON><PERSON>", "operation": "Scrape A Url And Get Its Content", "url": "https://zeli.app/zh", "formats": ["markdown"], "requestOptions": {}}, "type": "n8n-nodes-firecrawl.fireCrawl", "typeVersion": 1, "position": [320, -1296], "id": "56d91feb-5f95-4038-a331-2f52d17c62d4", "name": "FireCrawl5", "retryOnFail": true, "credentials": {"fireCrawlApi": {"id": "inHv0wGJZrsM6Duu", "name": "FireCrawl account"}}}, {"parameters": {"resource": "<PERSON><PERSON><PERSON>", "operation": "Scrape A Url And Get Its Content", "url": "https://newsnow.busiyi.world/c/realtime", "formats": ["markdown"], "requestOptions": {}}, "type": "n8n-nodes-firecrawl.fireCrawl", "typeVersion": 1, "position": [320, -1200], "id": "8b2e36a0-f5b7-459b-a098-bef1e57a067e", "name": "FireCrawl6", "retryOnFail": true, "credentials": {"fireCrawlApi": {"id": "inHv0wGJZrsM6Duu", "name": "FireCrawl account"}}}, {"parameters": {"numberInputs": 6}, "type": "n8n-nodes-base.merge", "typeVersion": 3.1, "position": [416, -1392], "id": "352123a5-83d0-4728-8509-8420f00622d9", "name": "<PERSON><PERSON>"}, {"parameters": {"promptType": "define", "text": "={{ $json.output }}", "options": {"systemMessage": "你是一位专业的新闻摘要助手。请严格遵循以下指示处理新闻：\n\n1.  **核心任务**：总结新闻内容。\n2.  **语言要求**：必须使用中文，如果输入不是中文，那么翻译为中文后输出。\n3.  **排序规则**：将所有新闻条目按照其**重要程度从高到低**排列。\n4.  **输出格式**：\n    *   **纯文本**：绝对不要使用任何Markdown语法（例如 `*`, `#`, `>` 等）。\n    *   **表情符号规则**：\n        *   **警报表情 (🚨)**：如果你判断某条新闻“非常非常重要”，则在该新闻摘要内容的**最前方**添加警报表情符号 “🚨”。\n        *   **类别/主题表情**：在每条新闻摘要内容的**末尾**，紧跟一个**单个空格**后，添加**一个且仅一个**能够**精确概括**该新闻**类别或主题**的表情符号。\n    *   **内容结尾**：新闻摘要内容的末尾（在单个空格和类别/主题表情符号之前）**不要加句号**。\n    *   **禁止数字标签**：删除新闻条目原始文本中可能存在于开头的数字、小数点或序号（如 “1.”、“6.8”、“5.5” 等）。\n5.  **最终输出格式示例**：\n    *   非常重要新闻：🚨新闻摘要内容 [精确概括类别/主题的单一表情]\n    *   普通重要新闻：新闻摘要内容 [精确概括类别/主题的单一表情]\n6.  **删除不重要的新闻**：你可以删除不重要的新闻，只输出哪些比较重要的。\n7.  **注意过去输出过的新闻就不要再次输出了**\n    例如：\n    🚨某国宣布进入紧急状态影响全球供应链 🌍\n    央行宣布降息以刺激经济 💰\n    国家队在世界杯预选赛中获胜 ⚽️\n    科技公司发布新款智能手机 📱\n\n请确保严格按照以上所有要求输出。"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.9, "position": [512, -1392], "id": "159fda10-926f-4c01-a10e-1899d77a8dfa", "name": "1-12"}, {"parameters": {"modelName": "models/gemini-2.5-flash-preview-04-17", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "typeVersion": 1, "position": [528, -1376], "id": "24f9a400-28dc-4389-aa25-0f75d61ac666", "name": "Google Gemini Chat Model20", "credentials": {"googlePalmApi": {"id": "gAJYDviDPrX1pHEq", "name": "Google Gemini(PaLM) Api account 2"}}}, {"parameters": {"resource": "<PERSON><PERSON><PERSON>", "operation": "Scrape A Url And Get Its Content", "url": "https://www.jinse.cn/lives", "formats": ["markdown"], "requestOptions": {}}, "type": "n8n-nodes-firecrawl.fireCrawl", "typeVersion": 1, "position": [320, -1104], "id": "ba85bbfa-3d61-461e-8ae9-3560b8bcd75e", "name": "FireCrawl19", "retryOnFail": true, "credentials": {"fireCrawlApi": {"id": "inHv0wGJZrsM6Duu", "name": "FireCrawl account"}}}, {"parameters": {"resource": "<PERSON><PERSON><PERSON>", "operation": "Scrape A Url And Get Its Content", "url": "https://www.aicoin.com/zh-Hans/news-flash", "formats": ["markdown"], "requestOptions": {}}, "type": "n8n-nodes-firecrawl.fireCrawl", "typeVersion": 1, "position": [320, -1008], "id": "9653fb70-d383-4df8-8b67-b44afee7827c", "name": "FireCrawl20", "retryOnFail": true, "credentials": {"fireCrawlApi": {"id": "inHv0wGJZrsM6Duu", "name": "FireCrawl account"}}}, {"parameters": {"resource": "<PERSON><PERSON><PERSON>", "operation": "Scrape A Url And Get Its Content", "url": "https://www.theblockbeats.info/newsflash", "formats": ["markdown"], "requestOptions": {}}, "type": "n8n-nodes-firecrawl.fireCrawl", "typeVersion": 1, "position": [320, -896], "id": "f80ae880-c23b-466f-8265-722b8ae40fb0", "name": "FireCrawl21", "retryOnFail": true, "credentials": {"fireCrawlApi": {"id": "inHv0wGJZrsM6Duu", "name": "FireCrawl account"}}}, {"parameters": {"resource": "<PERSON><PERSON><PERSON>", "operation": "Scrape A Url And Get Its Content", "url": "https://www.odaily.news/newsflash", "formats": ["markdown"], "requestOptions": {}}, "type": "n8n-nodes-firecrawl.fireCrawl", "typeVersion": 1, "position": [320, -1392], "id": "e736f46d-1469-4bb3-96db-c92a170b14bf", "name": "FireCrawl22", "retryOnFail": true, "credentials": {"fireCrawlApi": {"id": "inHv0wGJZrsM6Duu", "name": "FireCrawl account"}}}, {"parameters": {}, "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "typeVersion": 1.3, "position": [608, -1376], "id": "d2ca719c-8554-462b-944e-5555e2da1b70", "name": "Simple Memory"}], "pinData": {}, "connections": {"Schedule Trigger4": {"main": [[{"node": "FireCrawl5", "type": "main", "index": 0}, {"node": "FireCrawl6", "type": "main", "index": 0}, {"node": "FireCrawl19", "type": "main", "index": 0}, {"node": "FireCrawl20", "type": "main", "index": 0}, {"node": "FireCrawl21", "type": "main", "index": 0}, {"node": "FireCrawl22", "type": "main", "index": 0}]]}, "FireCrawl5": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 1}]]}, "FireCrawl6": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 2}]]}, "Merge": {"main": [[{"node": "1-12", "type": "main", "index": 0}]]}, "Google Gemini Chat Model20": {"ai_languageModel": [[{"node": "1-12", "type": "ai_languageModel", "index": 0}]]}, "1-12": {"main": [[{"node": "Telegram", "type": "main", "index": 0}]]}, "FireCrawl19": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 3}]]}, "FireCrawl20": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 4}]]}, "FireCrawl21": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 5}]]}, "FireCrawl22": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Simple Memory": {"ai_memory": [[{"node": "1-12", "type": "ai_memory", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1"}, "versionId": "5ba6e53e-18ab-4cc6-a07d-e34ae64a22cf", "meta": {"templateCredsSetupCompleted": true, "instanceId": "ab79ca41e488f6e0ed0db336857a0eb9ff536462f80fb4ab7db693a56ec15574"}, "id": "8YFC9JraJv2t82KF", "tags": []}